# Incremental Update Improvements - LanceDB Hybrid Search

## Issues Identified and Fixed

### 1. **No Immediate Incremental Check on Startup**
- **Problem**: System only scheduled incremental updates every 2 minutes, but didn't check for new records immediately on startup
- **Impact**: New records could sit unindexed for up to 2 minutes after system restart

### 2. **Unreliable Timestamp-Based Detection**
- **Problem**: Used `ETL_Proc_Time > last_update_time` which can miss records if timestamps are unreliable
- **Impact**: Could miss records if ETL processing timestamps are inconsistent

### 3. **No ID-Based Comparison Logic**
- **Problem**: Didn't use the robust temp table + ID comparison approach from backend_api.py
- **Impact**: Less reliable incremental updates compared to the proven backend_api.py approach

## Solutions Implemented

### 1. **Added Immediate Incremental Check on Startup**

```python
def run_immediate_incremental_check():
    """
    Run an immediate incremental check on startup
    Uses ID-based detection for maximum reliability
    """
    # Runs automatically when system starts
    # Uses ID-based detection for reliability
    # Only runs if existing data is found
```

**Benefits**:
- ✅ New records are detected and indexed immediately on startup
- ✅ No waiting period for incremental updates
- ✅ System stays current even after restarts

### 2. **Implemented ID-Based Detection (from backend_api.py)**

```python
def detect_new_records_by_id(existing_ids: List[str]) -> List[Dict]:
    """
    Detect new/updated records using ID-based comparison with temp table
    More reliable than timestamp-based detection (adapted from backend_api.py)
    """
    # Creates temp table with existing IDs
    # Uses NOT EXISTS query to find missing records
    # Handles deadlocks and retries
    # Cleans up temp table automatically
```

**Key Features**:
- ✅ **Temp Table Approach**: Creates `{table}_lancedb_ids` temp table with existing IDs
- ✅ **NOT EXISTS Query**: Uses `NOT EXISTS` to find records missing from LanceDB
- ✅ **Batch Processing**: Inserts IDs in batches of 1000 to avoid query size limits
- ✅ **Deadlock Handling**: Includes retry logic for SQL Server deadlocks
- ✅ **Automatic Cleanup**: Cleans up temp table after use

### 3. **Enhanced Unified Detection Function**

```python
def detect_new_records(last_update_time: Optional[datetime] = None, 
                      existing_ids: Optional[List[str]] = None) -> List[Dict]:
    """
    Unified function to detect new records using either ID-based or timestamp-based approach
    Prefers ID-based approach when existing_ids are provided
    """
```

**Logic**:
1. **ID-based (Preferred)**: If `existing_ids` provided → Use temp table approach
2. **Timestamp-based (Fallback)**: If `last_update_time` provided → Use timestamp comparison
3. **Default**: Use 24-hour lookback with timestamp approach

### 4. **Updated Incremental Update Function**

```python
def run_incremental_updates(use_id_based: bool = True):
    """
    Main incremental update loop with configurable detection method
    """
    if use_id_based:
        # Get existing IDs from LanceDB
        existing_ids = get_existing_ids_from_lancedb()
        new_records = detect_new_records(existing_ids=existing_ids)
    else:
        # Fallback to timestamp-based
        last_update = get_last_update_time()
        new_records = detect_new_records(last_update_time=last_update)
```

## System Flow Changes

### Before (Original)
```
1. System starts
2. Schedule incremental updates every 2 minutes
3. Wait 2 minutes for first check
4. Use timestamp-based detection: ETL_Proc_Time > last_update
5. Process any found records
```

### After (Improved)
```
1. System starts
2. Run immediate incremental check using ID-based detection
3. Schedule regular incremental updates every 2 minutes
4. Regular updates use ID-based detection by default
5. Timestamp-based detection available as fallback
```

## Technical Implementation

### ID-Based Detection Process
1. **Get Existing IDs**: Extract all IDs from active LanceDB table
2. **Create Temp Table**: `{table}_lancedb_ids` with existing IDs
3. **Batch Insert**: Insert IDs in 1000-record batches
4. **Query Missing**: Use NOT EXISTS to find missing records
5. **Process Results**: Convert to LanceDB format
6. **Cleanup**: Drop temp table

### Startup Flow
1. **System Initialization**: Check for existing data
2. **Immediate Check**: Run ID-based incremental check
3. **Schedule Regular Updates**: Every 2 minutes using ID-based detection
4. **Fallback Available**: Timestamp-based detection if ID-based fails

## Configuration

### Frequency Settings
```python
INCREMENTAL_FREQUENCY_MINUTES = 2      # Regular update frequency
INCREMENTAL_BATCH_SIZE = 1000          # Records per batch
```

### Detection Method
- **Default**: ID-based detection (more reliable)
- **Fallback**: Timestamp-based detection
- **Configurable**: Can switch methods via parameter

## Benefits

### 1. **Immediate Updates**
- ✅ New records indexed immediately on startup
- ✅ No waiting period for incremental updates
- ✅ System always current

### 2. **Reliability**
- ✅ ID-based detection catches all missing records
- ✅ Doesn't rely on potentially unreliable timestamps
- ✅ Proven approach from backend_api.py

### 3. **Robustness**
- ✅ Handles SQL Server deadlocks with retry logic
- ✅ Automatic temp table cleanup
- ✅ Fallback detection method available

### 4. **Performance**
- ✅ Efficient batch processing
- ✅ Minimal memory usage for ID extraction
- ✅ Database-side comparison using temp tables

## Usage

### Automatic (Default)
- System automatically runs immediate check on startup
- Regular updates use ID-based detection
- No configuration needed

### Manual Control
```python
# Force timestamp-based detection
run_incremental_updates(use_id_based=False)

# Run immediate check manually
run_immediate_incremental_check()

# Direct ID-based detection
existing_ids = get_existing_ids_from_lancedb()
new_records = detect_new_records(existing_ids=existing_ids)
```

## Comparison with backend_api.py

The implementation closely follows the proven approach from `backend_api.py`:

| Feature | backend_api.py | LanceDB Implementation |
|---------|----------------|----------------------|
| Temp Table | `{table}_cache_ids` | `{table}_lancedb_ids` |
| Batch Size | 1000 records | 1000 records |
| Deadlock Handling | ✅ 5 retries | ✅ 3 retries |
| NOT EXISTS Query | ✅ | ✅ |
| Automatic Cleanup | ✅ | ✅ |
| Immediate Check | ✅ Manual trigger | ✅ Automatic on startup |

The LanceDB implementation provides the same reliability as the backend_api.py approach while being optimized for the LanceDB hybrid search use case.
