#!/usr/bin/env python3
"""
Test script to verify the timestamp precision fix
"""

import os
import sys
import pandas as pd
import pyarrow as pa
from datetime import datetime, timedelta
import time

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lancedb_hybrid_search import (
    get_primary_schema,
    get_metadata_schema,
    convert_timestamp,
    get_lancedb_connection,
    DB_PATH
)

def test_timestamp_precision():
    """Test that timestamp precision is preserved"""
    print("🧪 Testing Timestamp Precision Fix")
    print("=" * 50)
    
    # Test 1: Check schema definitions
    print("\n📋 Test 1: Checking schema definitions...")
    
    primary_schema = get_primary_schema()
    metadata_schema = get_metadata_schema()
    
    # Check primary schema timestamp fields
    timestamp_fields = ['entered_time', 'report_time', 'etl_proc_time']
    for field_name in timestamp_fields:
        field = primary_schema.field(field_name)
        print(f"  ✓ {field_name}: {field.type}")
        assert str(field.type) == "timestamp[ns]", f"Expected timestamp[ns], got {field.type}"
    
    # Check metadata schema timestamp fields
    metadata_timestamp_fields = ['last_updated']
    for field_name in metadata_timestamp_fields:
        field = metadata_schema.field(field_name)
        print(f"  ✓ {field_name}: {field.type}")
        assert str(field.type) == "timestamp[ns]", f"Expected timestamp[ns], got {field.type}"
    
    # Check switch_history field (list of timestamps)
    switch_history_field = metadata_schema.field('switch_history')
    print(f"  ✓ switch_history: {switch_history_field.type}")
    assert "timestamp[ns]" in str(switch_history_field.type), f"Expected list of timestamp[ns], got {switch_history_field.type}"
    
    print("  ✅ All schema timestamp fields use nanosecond precision!")
    
    # Test 2: Test timestamp conversion function
    print("\n🔄 Test 2: Testing timestamp conversion function...")
    
    # Test various timestamp formats
    test_cases = [
        "2024-01-15 10:30:45.123456789",  # Nanosecond precision
        "2024-01-15 10:30:45.123456",     # Microsecond precision
        "2024-01-15 10:30:45.123",        # Millisecond precision
        "2024-01-15 10:30:45",            # Second precision
        "2024-01-15T10:30:45.123456Z",    # ISO format with microseconds
        datetime.now(),                    # datetime object
        None                              # None value
    ]
    
    for i, test_value in enumerate(test_cases):
        result = convert_timestamp(test_value)
        print(f"  Test {i+1}: {test_value} -> {result}")
        
        if test_value is not None:
            assert isinstance(result, datetime) or result is None, f"Expected datetime or None, got {type(result)}"
        else:
            assert result is None, "Expected None for None input"
    
    print("  ✅ Timestamp conversion function works correctly!")
    
    # Test 3: Test creating a table with high-precision timestamps
    print("\n📊 Test 3: Testing table creation with high-precision timestamps...")
    
    try:
        # Create test data with nanosecond precision
        now = datetime.now()
        test_data = {
            "id": ["test_1", "test_2"],
            "vector": [[0.1] * 384, [0.2] * 384],
            "text": ["Test text 1", "Test text 2"],
            "type": ["test", "test"],
            "niche_report_id": ["report_1", "report_2"],
            "entered_time": [now, now + timedelta(microseconds=123456)],
            "report_time": [now, now + timedelta(microseconds=654321)],
            "zone": ["zone1", "zone2"],
            "team": ["team1", "team2"],
            "municipality": ["city1", "city2"],
            "category": ["cat1", "cat2"],
            "author_id": ["author1", "author2"],
            "occurrence_id": ["occ1", "occ2"],
            "text_length": [11, 11],
            "etl_proc_time": [now, now + timedelta(microseconds=999999)],
            "update_version": [1, 2]
        }
        
        df = pd.DataFrame(test_data)
        print(f"  Created test DataFrame with {len(df)} rows")
        
        # Try to create a PyArrow table with the schema
        schema = get_primary_schema()
        pa_table = pa.Table.from_pandas(df, schema=schema)
        print(f"  ✅ Successfully created PyArrow table with nanosecond precision!")
        print(f"  Table schema: {pa_table.schema}")
        
        # Test creating LanceDB table
        test_db_path = "./test_lancedb_data"
        os.makedirs(test_db_path, exist_ok=True)
        
        db = get_lancedb_connection(test_db_path)
        test_table_name = "test_timestamp_precision"
        
        # Drop table if it exists
        if test_table_name in db.table_names():
            db.drop_table(test_table_name)
        
        # Create table
        table = db.create_table(test_table_name, df, schema=schema)
        print(f"  ✅ Successfully created LanceDB table!")
        
        # Read back and verify
        result_df = table.to_pandas()
        print(f"  Read back {len(result_df)} rows")
        
        # Check timestamp precision is preserved
        for ts_field in ['entered_time', 'report_time', 'etl_proc_time']:
            original_ts = df[ts_field].iloc[0]
            read_ts = result_df[ts_field].iloc[0]
            print(f"  {ts_field}: {original_ts} -> {read_ts}")
        
        # Clean up
        db.drop_table(test_table_name)
        print(f"  🧹 Cleaned up test table")
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False
    
    # Test 4: Test metadata table creation
    print("\n📋 Test 4: Testing metadata table creation...")
    
    try:
        metadata_data = {
            "table_name": ["test_table"],
            "status": ["active"],
            "record_count": [1000],
            "last_updated": [datetime.now()],
            "version": [1],
            "health_status": ["healthy"],
            "switch_history": [[datetime.now()]]
        }
        
        metadata_df = pd.DataFrame(metadata_data)
        metadata_schema = get_metadata_schema()
        
        # Create PyArrow table
        metadata_pa_table = pa.Table.from_pandas(metadata_df, schema=metadata_schema)
        print(f"  ✅ Successfully created metadata PyArrow table!")
        
        # Test LanceDB metadata table
        test_metadata_table = "test_metadata"
        if test_metadata_table in db.table_names():
            db.drop_table(test_metadata_table)
        
        metadata_table = db.create_table(test_metadata_table, metadata_df, schema=metadata_schema)
        print(f"  ✅ Successfully created LanceDB metadata table!")
        
        # Clean up
        db.drop_table(test_metadata_table)
        print(f"  🧹 Cleaned up test metadata table")
        
    except Exception as e:
        print(f"  ❌ Metadata test failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All timestamp precision tests passed!")
    print("\nSummary of fixes:")
    print("✅ Changed all timestamp fields from 'ms' to 'ns' precision")
    print("✅ Updated convert_timestamp function to preserve full precision")
    print("✅ Removed precision truncation logic")
    print("✅ All function calls updated to use new function name")
    print("\nThe timestamp casting error should now be resolved!")
    
    return True

if __name__ == "__main__":
    test_timestamp_precision()
