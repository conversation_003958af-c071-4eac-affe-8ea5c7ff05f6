#!/usr/bin/env python3
"""
Test script to demonstrate the improved existing data checking functionality
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lancedb_hybrid_search import (
    check_existing_data,
    initialize_system,
    get_system_status,
    DB_PATH,
    PRIMARY_TABLE,
    SHADOW_TABLE,
    METADATA_TABLE
)

def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_existing_data_check():
    """Test the existing data checking functionality"""
    logger = setup_test_logging()
    
    print("🧪 Testing LanceDB Hybrid Search - Existing Data Check")
    print("=" * 60)
    
    # Test 1: Check existing data status
    print("\n📋 Test 1: Checking existing data status...")
    try:
        status = check_existing_data()
        
        print(f"✓ Check completed successfully")
        print(f"  - Has Primary Table: {status['has_primary']}")
        print(f"  - Has Shadow Table: {status['has_shadow']}")
        print(f"  - Has Metadata Table: {status['has_metadata']}")
        print(f"  - Primary Records: {status['primary_count']:,}")
        print(f"  - Shadow Records: {status['shadow_count']:,}")
        print(f"  - Can Resume: {status['can_resume']}")
        print(f"  - Needs Initialization: {status['needs_initialization']}")
        
        if status['metadata_info']:
            print(f"  - Active Table: {status['metadata_info'].get('active_table', 'Unknown')}")
            print(f"  - Last Updated: {status['metadata_info'].get('last_updated', 'Unknown')}")
        
    except Exception as e:
        print(f"✗ Test 1 failed: {e}")
        return False
    
    # Test 2: Test initialization logic
    print("\n🚀 Test 2: Testing initialization logic...")
    try:
        # Test with existing data (should not rebuild)
        print("  Testing initialization with existing data check...")
        success = initialize_system(force_rebuild=False)
        
        if success:
            print("  ✓ Initialization completed successfully")
        else:
            print("  ⚠ Initialization indicated no existing data or failed")
        
    except Exception as e:
        print(f"  ✗ Test 2 failed: {e}")
        return False
    
    # Test 3: Get system status
    print("\n📊 Test 3: Getting system status...")
    try:
        system_status = get_system_status()
        
        if system_status["success"]:
            print("  ✓ System status retrieved successfully")
            if "active_table" in system_status:
                active_info = system_status["active_table"]
                print(f"    - Active Table: {active_info.get('name', 'Unknown')}")
                print(f"    - Record Count: {active_info.get('record_count', 0):,}")
                print(f"    - Health: {active_info.get('health', 'Unknown')}")
            
            if "performance" in system_status:
                perf = system_status["performance"]
                print(f"    - Query Latency: {perf.get('avg_query_latency_ms', 0):.1f}ms")
                print(f"    - Memory Usage: {perf.get('memory_usage_mb', 0):.1f}MB")
        else:
            print(f"  ⚠ System status check failed: {system_status.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"  ✗ Test 3 failed: {e}")
        return False
    
    # Test 4: Database path and table information
    print("\n📁 Test 4: Database and table information...")
    try:
        print(f"  Database Path: {DB_PATH}")
        print(f"  Primary Table Name: {PRIMARY_TABLE}")
        print(f"  Shadow Table Name: {SHADOW_TABLE}")
        print(f"  Metadata Table Name: {METADATA_TABLE}")
        
        # Check if database directory exists
        if os.path.exists(DB_PATH):
            print(f"  ✓ Database directory exists")
            files = os.listdir(DB_PATH)
            print(f"  Database contains {len(files)} files/directories")
        else:
            print(f"  ⚠ Database directory does not exist yet")
        
    except Exception as e:
        print(f"  ✗ Test 4 failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed!")
    print("\nNext steps:")
    print("1. Run 'python lancedb_hybrid_search.py status' to check system status")
    print("2. Run 'python lancedb_hybrid_search.py' to start the system (uses existing data)")
    print("3. Run 'python lancedb_hybrid_search.py rebuild' to force rebuild from SQL Server")
    
    return True

if __name__ == "__main__":
    test_existing_data_check()
