lancedb-0.24.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lancedb-0.24.2.dist-info/METADATA,sha256=JmDFhb278z5XW1sjW4EddtBGO4Z4qUlB3o7mqWP1DmU,4477
lancedb-0.24.2.dist-info/RECORD,,
lancedb-0.24.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lancedb-0.24.2.dist-info/WHEEL,sha256=IOvNb_Dp11OuRIpo2OWqTSUXKUtcHN63pkGBkO879SE,94
lancedb-0.24.2.dist-info/licenses/LICENSE,sha256=SbvpEU5JIU3yzMMkyzrI0dGqHDoJR_lMKGdl6GZHsy4,11558
lancedb/__init__.py,sha256=vAjNi31VMulujjbJR65i3JCnpsNOepuWjsMrhk0OQdE,9226
lancedb/__pycache__/__init__.cpython-312.pyc,,
lancedb/__pycache__/arrow.cpython-312.pyc,,
lancedb/__pycache__/background_loop.cpython-312.pyc,,
lancedb/__pycache__/common.cpython-312.pyc,,
lancedb/__pycache__/conftest.cpython-312.pyc,,
lancedb/__pycache__/context.cpython-312.pyc,,
lancedb/__pycache__/db.cpython-312.pyc,,
lancedb/__pycache__/dependencies.cpython-312.pyc,,
lancedb/__pycache__/exceptions.cpython-312.pyc,,
lancedb/__pycache__/fts.cpython-312.pyc,,
lancedb/__pycache__/index.cpython-312.pyc,,
lancedb/__pycache__/merge.cpython-312.pyc,,
lancedb/__pycache__/pydantic.cpython-312.pyc,,
lancedb/__pycache__/query.cpython-312.pyc,,
lancedb/__pycache__/schema.cpython-312.pyc,,
lancedb/__pycache__/table.cpython-312.pyc,,
lancedb/__pycache__/types.cpython-312.pyc,,
lancedb/__pycache__/util.cpython-312.pyc,,
lancedb/_lancedb.pyd,sha256=_dpo3AvLYJ7OoCJFEXFY51eAAoikzeankNXeaAGtHsg,113171456
lancedb/_lancedb.pyi,sha256=IAPUn5Z-oIzj4u2clCejN6VSsefrBFtlPlPuxO6U9FI,8451
lancedb/arrow.py,sha256=I2wEcY1ATxwY1JWTNiRr0Ra2qnUwlcMj4VDsqxsLOQ0,2502
lancedb/background_loop.py,sha256=Ichoz9bx2NcPMF3DLmb_XfofNfVj5ZC4b-7dus4WQPE,728
lancedb/common.py,sha256=72Qt3cAQXhfvLW1CP0FZRc93DxgxfMTPNNKo-pNCggw,4543
lancedb/conftest.py,sha256=kkjlC-Gv4gjJvVn6TQOoSnMdWv3VJnWX_OQo-iN32Ks,2502
lancedb/context.py,sha256=Ts9W2giMMUVfAf0vd2_c5Zmdl0jkCHvrVRJo8fYoyz0,8692
lancedb/db.py,sha256=srJr4vSGgXCG6oy1VfZYmY7IKnN2aQck3jLe0t-pajI,31959
lancedb/dependencies.py,sha256=hEDmGlboekuv8Kp43-H7uLN4qBlONGO9fATCC7CC7PM,8686
lancedb/embeddings/__init__.py,sha256=5bwNV9psp9B-dl1IZyoGHyVIDLnpy9P6nmH4U-6CyWo,949
lancedb/embeddings/__pycache__/__init__.cpython-312.pyc,,
lancedb/embeddings/__pycache__/base.cpython-312.pyc,,
lancedb/embeddings/__pycache__/bedrock.cpython-312.pyc,,
lancedb/embeddings/__pycache__/cohere.cpython-312.pyc,,
lancedb/embeddings/__pycache__/colpali.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gemini_text.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gte.cpython-312.pyc,,
lancedb/embeddings/__pycache__/gte_mlx_model.cpython-312.pyc,,
lancedb/embeddings/__pycache__/imagebind.cpython-312.pyc,,
lancedb/embeddings/__pycache__/instructor.cpython-312.pyc,,
lancedb/embeddings/__pycache__/jinaai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/ollama.cpython-312.pyc,,
lancedb/embeddings/__pycache__/open_clip.cpython-312.pyc,,
lancedb/embeddings/__pycache__/openai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/registry.cpython-312.pyc,,
lancedb/embeddings/__pycache__/sentence_transformers.cpython-312.pyc,,
lancedb/embeddings/__pycache__/transformers.cpython-312.pyc,,
lancedb/embeddings/__pycache__/utils.cpython-312.pyc,,
lancedb/embeddings/__pycache__/voyageai.cpython-312.pyc,,
lancedb/embeddings/__pycache__/watsonx.cpython-312.pyc,,
lancedb/embeddings/base.py,sha256=uTQQx3rwDRjRiATSucz6ibnRdy2uYmuoZQ3eLRBBMUg,8041
lancedb/embeddings/bedrock.py,sha256=OgR4v0tgbwxVQGIdC1GBOKwOpVDZfJveTXDyaS8rA18,7949
lancedb/embeddings/cohere.py,sha256=a_7AzXrNnQmxE3b0Qln5VTjwtByJ-KVBgUZHey848hM,4960
lancedb/embeddings/colpali.py,sha256=VbUcZtN1Tcjo9d6kDEmk7So40f6Dcu5lXa-qjTb4Qec,9689
lancedb/embeddings/gemini_text.py,sha256=b9b_6p0fKqF5yVWm2LD-qJVJnRwuAnCLk7Lkt6cz_nQ,5161
lancedb/embeddings/gte.py,sha256=XKPHCOxYuh5pmOn-SwTaJlsVh2EodTUyOc8SgTnuU8s,3886
lancedb/embeddings/gte_mlx_model.py,sha256=qlHKEg39_KEehGsM1KtnIjwXZdUdL39mXDL15xh0S5s,5403
lancedb/embeddings/imagebind.py,sha256=9mDToBYfO7zdsBh2mmqOcmenfGx3hDT3a3osP7lgYkc,6069
lancedb/embeddings/instructor.py,sha256=VpuubolRBl18_GN3SwUAVMShw3yMpQw2pfh2vYvk29A,5719
lancedb/embeddings/jinaai.py,sha256=VrBH1Qdu7jFcSwXIemH_NTRNas4iAWA7GlO6tvbt7cc,8370
lancedb/embeddings/ollama.py,sha256=Iw_Bk0rat9oF6MOxGFfJwOoAlM-tzbKL5qDNzbw3Uss,1985
lancedb/embeddings/open_clip.py,sha256=j61qjkhi3Mhw7JCZZNwcBWTzd_rTlsDXY2IYAKUhfQw,6311
lancedb/embeddings/openai.py,sha256=hBhNzNgWnVT_2BxRDWB3b6whdA1VJX2ioX1EX3-lwN8,4119
lancedb/embeddings/registry.py,sha256=5BCWkwrjeptQbksIxa2RIl2zrciPcP4AsIZ8pZCPrwo,7341
lancedb/embeddings/sentence_transformers.py,sha256=WcB_3WBk7aFbOeqn5hjNW6mMS1-VLRwNLlcBfiR_nQo,2719
lancedb/embeddings/transformers.py,sha256=EbfHn878XvMwf0hMse4LBanGmTfTa9RWQsPX5h6ga-g,3889
lancedb/embeddings/utils.py,sha256=g1WMTV-Rb8GrEGY8_o3V4hlwmdipDWLCCxvqM7SgKa0,9236
lancedb/embeddings/voyageai.py,sha256=vTV0btr5XlUlc7f2B3mxo7bhKUvibSIHEii6WwqHe1k,8186
lancedb/embeddings/watsonx.py,sha256=0jF4RuwpR2tZM-KvV2OpS_BVT39FhmA60_HOTHgf7aI,3278
lancedb/exceptions.py,sha256=LcY4mSjDxoXiMtE1nSTRmdQ_dReRaCNPshp94dYPYOQ,619
lancedb/fts.py,sha256=lgRQ7fZ9SHDX7nFDc-94-lHHq35o1B5mybiVwczl8Pc,5865
lancedb/index.py,sha256=BzePeeXG0X8N5pqPU7QLy3K05BrNq4CajS_2ldz0Zm4,24545
lancedb/integrations/__init__.py,sha256=RH29ff79Zgb6k00OoIMMm08_R0esLeZSqczWKKyJdHM,96
lancedb/integrations/__pycache__/__init__.cpython-312.pyc,,
lancedb/integrations/__pycache__/pyarrow.cpython-312.pyc,,
lancedb/integrations/pyarrow.py,sha256=-R2dqi_VticrpfEx2Uy7JZ6uqmVTXxOE3qVPy78dtuk,7630
lancedb/merge.py,sha256=i0ZauEW0B-XuxGJq6Ephyyb2rACtqK7WK86td0ssI9A,4762
lancedb/pydantic.py,sha256=52rJvR07sBYDwrRUjeeIk8XztJKDI4muJIVqhXGaYP4,16072
lancedb/query.py,sha256=RgS6z-ItxMLr8WJluSnlgmc14bcJj0W_LOyOgahWGJU,115675
lancedb/remote/__init__.py,sha256=mxvE7eHbSss4_FSo0o9wQUNcOsLiJiJnoCMKHbVdgbA,4922
lancedb/remote/__pycache__/__init__.cpython-312.pyc,,
lancedb/remote/__pycache__/db.cpython-312.pyc,,
lancedb/remote/__pycache__/errors.cpython-312.pyc,,
lancedb/remote/__pycache__/table.cpython-312.pyc,,
lancedb/remote/db.py,sha256=XNHyfDxswmv2iwrK8KC7m8H6Bofdlzu_EwssZuVMUrM,10058
lancedb/remote/errors.py,sha256=_RPYGj14wA02P-vgVNkJetBhH6stG6M4RASiWgwlWFs,3471
lancedb/remote/table.py,sha256=1_841Xz6U3ySowShRuYJaG9KxpcSMoF3BElb4YcFjhM,22603
lancedb/rerankers/__init__.py,sha256=gkivGE6oZ3J9lJAQCawPP0C0iO4-quTJ7uhI95YN2Ws,758
lancedb/rerankers/__pycache__/__init__.cpython-312.pyc,,
lancedb/rerankers/__pycache__/answerdotai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/base.cpython-312.pyc,,
lancedb/rerankers/__pycache__/cohere.cpython-312.pyc,,
lancedb/rerankers/__pycache__/colbert.cpython-312.pyc,,
lancedb/rerankers/__pycache__/cross_encoder.cpython-312.pyc,,
lancedb/rerankers/__pycache__/jinaai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/linear_combination.cpython-312.pyc,,
lancedb/rerankers/__pycache__/openai.cpython-312.pyc,,
lancedb/rerankers/__pycache__/rrf.cpython-312.pyc,,
lancedb/rerankers/__pycache__/util.cpython-312.pyc,,
lancedb/rerankers/__pycache__/voyageai.cpython-312.pyc,,
lancedb/rerankers/answerdotai.py,sha256=-gKnEe_vmfOs07ekIVfIo8H5xA9pcJ_iMSzZ5TpFss8,3626
lancedb/rerankers/base.py,sha256=VisUbW3cdr8xftoyhyQo6l7vdG6rDbzAswE_4w6-Z8A,9890
lancedb/rerankers/cohere.py,sha256=I5iLNf3fq6Xb5008tb4-UzTwVyeFZhXr4Wp-2Z6V1DU,4106
lancedb/rerankers/colbert.py,sha256=hWtB21hqMnSa9YK2zLOR2rkVHoAV-JVLvJiLECr5PBk,1192
lancedb/rerankers/cross_encoder.py,sha256=ABqLrvP1LFZ7dh48MbmOeWsXsqKTJ1mCLT7EWBOWAN4,4229
lancedb/rerankers/jinaai.py,sha256=GXdOgZfjDfHw2l1kgJ53bDzKYnvpU38PvGBPPMT58GI,4110
lancedb/rerankers/linear_combination.py,sha256=aqsRpEk2qrPWl4iQeIAt791UpJeu_MZWe24V9rCvvnc,4972
lancedb/rerankers/openai.py,sha256=d5iVicAL3-mRuLge1_uUAFq75V1PifZIZyK2iGexbqk,5151
lancedb/rerankers/rrf.py,sha256=2EkNlc1S-JouEuRwxIkeazIYsQ6cKW3b7t9mgQsleXk,4313
lancedb/rerankers/util.py,sha256=_zmrFaWllOYQOG6JpsOVdMBJ8mwAb8oVHVUZ7vHeo4Q,649
lancedb/rerankers/voyageai.py,sha256=o03pNYl--lVZRkf9pxG4ksoYIx733Jie3xdBUjNYGa0,4186
lancedb/schema.py,sha256=1Fki9Tr46UfUH3VYvBAuA1OjH8czOhrC9gFsBf6A6zc,799
lancedb/table.py,sha256=Cv8GxqlVlQqyuTXTKtsCKqE_djg7XC2MthxWEXOFmZM,161051
lancedb/types.py,sha256=M5tioqGrUAM7LrpeX8M25zvtZrq9RmpLEEzvp1wYcKo,914
lancedb/util.py,sha256=LM_gFECoo6LiLgnQnQZnWFAytgcV2r-eSaQwVqs9GkA,10334
