Metadata-Version: 2.4
Name: lancedb
Version: 0.24.2
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Console
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering
Requires-Dist: deprecation
Requires-Dist: numpy
Requires-Dist: overrides>=0.7
Requires-Dist: packaging
Requires-Dist: pyarrow>=16
Requires-Dist: pydantic>=1.10
Requires-Dist: tqdm>=4.27.0
Requires-Dist: pylance>=0.25 ; extra == 'pylance'
Requires-Dist: aiohttp ; extra == 'tests'
Requires-Dist: boto3 ; extra == 'tests'
Requires-Dist: pandas>=1.4 ; extra == 'tests'
Requires-Dist: pytest ; extra == 'tests'
Requires-Dist: pytest-mock ; extra == 'tests'
Requires-Dist: pytest-asyncio ; extra == 'tests'
Requires-Dist: duckdb ; extra == 'tests'
Requires-Dist: pytz ; extra == 'tests'
Requires-Dist: polars>=0.19,<=1.3.0 ; extra == 'tests'
Requires-Dist: tantivy ; extra == 'tests'
Requires-Dist: pyarrow-stubs ; extra == 'tests'
Requires-Dist: pylance>=0.25 ; extra == 'tests'
Requires-Dist: requests ; extra == 'tests'
Requires-Dist: datafusion ; extra == 'tests'
Requires-Dist: ruff ; extra == 'dev'
Requires-Dist: pre-commit ; extra == 'dev'
Requires-Dist: pyright ; extra == 'dev'
Requires-Dist: typing-extensions>=4.0.0 ; python_full_version < '3.11' and extra == 'dev'
Requires-Dist: mkdocs ; extra == 'docs'
Requires-Dist: mkdocs-jupyter ; extra == 'docs'
Requires-Dist: mkdocs-material ; extra == 'docs'
Requires-Dist: mkdocstrings[python] ; extra == 'docs'
Requires-Dist: torch ; extra == 'clip'
Requires-Dist: pillow ; extra == 'clip'
Requires-Dist: open-clip-torch ; extra == 'clip'
Requires-Dist: requests>=2.31.0 ; extra == 'embeddings'
Requires-Dist: openai>=1.6.1 ; extra == 'embeddings'
Requires-Dist: sentence-transformers ; extra == 'embeddings'
Requires-Dist: torch ; extra == 'embeddings'
Requires-Dist: pillow ; extra == 'embeddings'
Requires-Dist: open-clip-torch ; extra == 'embeddings'
Requires-Dist: cohere ; extra == 'embeddings'
Requires-Dist: colpali-engine>=0.3.10 ; extra == 'embeddings'
Requires-Dist: huggingface-hub ; extra == 'embeddings'
Requires-Dist: instructorembedding ; extra == 'embeddings'
Requires-Dist: google-generativeai ; extra == 'embeddings'
Requires-Dist: boto3>=1.28.57 ; extra == 'embeddings'
Requires-Dist: awscli>=1.29.57 ; extra == 'embeddings'
Requires-Dist: botocore>=1.31.57 ; extra == 'embeddings'
Requires-Dist: ibm-watsonx-ai>=1.1.2 ; python_full_version >= '3.10' and extra == 'embeddings'
Requires-Dist: ollama>=0.3.0 ; extra == 'embeddings'
Requires-Dist: adlfs>=2024.2.0 ; extra == 'azure'
Provides-Extra: pylance
Provides-Extra: tests
Provides-Extra: dev
Provides-Extra: docs
Provides-Extra: clip
Provides-Extra: embeddings
Provides-Extra: azure
License-File: LICENSE
Summary: lancedb
Keywords: data-format,data-science,machine-learning,arrow,data-analytics
Author-email: LanceDB Devs <<EMAIL>>
License: Apache-2.0
Requires-Python: >=3.9
Description-Content-Type: text/markdown; charset=UTF-8; variant=GFM
Project-URL: repository, https://github.com/lancedb/lancedb

# LanceDB

A Python library for [LanceDB](https://github.com/lancedb/lancedb).

## Installation

```bash
pip install lancedb
```

### Preview Releases

Stable releases are created about every 2 weeks. For the latest features and bug fixes, you can install the preview release. These releases receive the same level of testing as stable releases, but are not guaranteed to be available for more than 6 months after they are released. Once your application is stable, we recommend switching to stable releases.


```bash
pip install --pre --extra-index-url https://pypi.fury.io/lancedb/ lancedb
```

## Usage

### Basic Example

```python
import lancedb
db = lancedb.connect('<PATH_TO_LANCEDB_DATASET>')
table = db.open_table('my_table')
results = table.search([0.1, 0.3]).limit(20).to_list()
print(results)
```

### Development

See [CONTRIBUTING.md](./CONTRIBUTING.md) for information on how to contribute to LanceDB.

