2025-08-05 13:30:46,451 - lancedb_hybrid_search - INFO - Starting LanceDB Hybrid Search API...
2025-08-05 13:30:46,452 - lancedb_hybrid_search - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:30:46,457 - lancedb_hybrid_search - INFO - No existing primary table found, performing initial data load
2025-08-05 13:30:46,458 - lancedb_hybrid_search - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:30:46,676 - lancedb_hybrid_search - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:30:46,676 - lancedb_hybrid_search - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:30:47,072 - lancedb_hybrid_search - ERROR - Failed to extract records in chunks: [Errno 22] Invalid argument
2025-08-05 13:30:47,073 - lancedb_hybrid_search - ERROR - Chunked full rebuild failed: [<PERSON>rrno 22] Invalid argument
2025-08-05 13:30:47,078 - lancedb_hybrid_search - ERROR - System initialization failed during initial data load
2025-08-05 13:30:47,078 - lancedb_hybrid_search - ERROR - Failed to initialize hybrid search system
2025-08-05 13:32:01,568 - lancedb_hybrid_search - INFO - Shutting down LanceDB Hybrid Search API...
2025-08-05 13:32:06,529 - __main__ - INFO - ============================================================
2025-08-05 13:32:06,530 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 13:32:06,530 - __main__ - INFO - ============================================================
2025-08-05 13:32:06,530 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:32:06,533 - __main__ - INFO - No existing primary table found, performing initial data load
2025-08-05 13:32:06,534 - __main__ - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:32:06,709 - __main__ - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:32:06,709 - __main__ - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:32:06,953 - __main__ - ERROR - Failed to extract records in chunks: [Errno 22] Invalid argument
2025-08-05 13:32:06,953 - __main__ - ERROR - Chunked full rebuild failed: [Errno 22] Invalid argument
2025-08-05 13:32:06,958 - __main__ - ERROR - System initialization failed during initial data load
2025-08-05 13:32:06,958 - __main__ - ERROR - System initialization failed, exiting
2025-08-05 13:32:06,958 - __main__ - INFO - LanceDB Hybrid Search System Stopped
2025-08-05 13:33:38,555 - __main__ - INFO - ============================================================
2025-08-05 13:33:38,555 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 13:33:38,555 - __main__ - INFO - ============================================================
2025-08-05 13:33:38,556 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:33:38,560 - __main__ - INFO - No existing primary table found, performing initial data load
2025-08-05 13:33:38,560 - __main__ - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:33:38,724 - __main__ - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:33:38,724 - __main__ - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:33:39,011 - __main__ - ERROR - Failed to extract chunk 1 at offset 0: [Errno 22] Invalid argument
2025-08-05 13:33:39,011 - __main__ - ERROR - Failed to extract records in chunks: [Errno 22] Invalid argument
2025-08-05 13:33:39,011 - __main__ - ERROR - Chunked full rebuild failed: [Errno 22] Invalid argument
2025-08-05 13:33:39,016 - __main__ - ERROR - System initialization failed during initial data load
2025-08-05 13:33:39,016 - __main__ - ERROR - System initialization failed, exiting
2025-08-05 13:33:39,017 - __main__ - INFO - LanceDB Hybrid Search System Stopped
2025-08-05 13:34:55,330 - __main__ - INFO - ============================================================
2025-08-05 13:34:55,331 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 13:34:55,331 - __main__ - INFO - ============================================================
2025-08-05 13:34:55,331 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:34:55,334 - __main__ - INFO - No existing primary table found, performing initial data load
2025-08-05 13:34:55,334 - __main__ - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:34:55,519 - __main__ - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:34:55,519 - __main__ - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:34:55,777 - __main__ - ERROR - Failed to extract chunk 1 at offset 0: [Errno 22] Invalid argument
2025-08-05 13:34:55,777 - __main__ - ERROR - Failed to extract records in chunks: [Errno 22] Invalid argument
2025-08-05 13:34:55,778 - __main__ - ERROR - Chunked full rebuild failed: [Errno 22] Invalid argument
2025-08-05 13:34:55,782 - __main__ - ERROR - System initialization failed during initial data load
2025-08-05 13:34:55,783 - __main__ - ERROR - System initialization failed, exiting
2025-08-05 13:34:55,783 - __main__ - INFO - LanceDB Hybrid Search System Stopped
2025-08-05 13:40:11,566 - lancedb_hybrid_search - INFO - Starting LanceDB Hybrid Search API...
2025-08-05 13:40:11,566 - lancedb_hybrid_search - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:40:11,569 - lancedb_hybrid_search - INFO - No existing primary table found, performing initial data load
2025-08-05 13:40:11,570 - lancedb_hybrid_search - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:40:11,736 - lancedb_hybrid_search - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:40:11,737 - lancedb_hybrid_search - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:40:12,053 - lancedb_hybrid_search - ERROR - Failed to extract records in chunks: [Errno 22] Invalid argument
2025-08-05 13:40:12,053 - lancedb_hybrid_search - ERROR - Chunked full rebuild failed: [Errno 22] Invalid argument
2025-08-05 13:40:12,059 - lancedb_hybrid_search - ERROR - System initialization failed during initial data load
2025-08-05 13:40:12,059 - lancedb_hybrid_search - ERROR - Failed to initialize hybrid search system
2025-08-05 13:43:02,491 - lancedb_hybrid_search - INFO - Shutting down LanceDB Hybrid Search API...
2025-08-05 13:45:11,506 - __main__ - INFO - ============================================================
2025-08-05 13:45:11,507 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 13:45:11,507 - __main__ - INFO - ============================================================
2025-08-05 13:45:11,507 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:45:11,511 - __main__ - INFO - No existing primary table found, performing initial data load
2025-08-05 13:45:11,512 - __main__ - INFO - Starting chunked full rebuild for shadow table
2025-08-05 13:45:11,678 - __main__ - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 13:45:11,678 - __main__ - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 13:45:20,493 - __main__ - INFO - Processing rebuild chunk 1: 46,449 records
2025-08-05 13:45:21,463 - __main__ - INFO - Created shadow table report_nlp_shadow with 46,449 records
2025-08-05 13:45:21,465 - __main__ - INFO - Extracting chunk 2: records 50,000 to 100,000
2025-08-05 13:45:31,811 - __main__ - INFO - Processing rebuild chunk 2: 47,884 records
2025-08-05 13:45:32,997 - __main__ - INFO - Added chunk 2 to shadow table: 47,884 records
2025-08-05 13:45:32,999 - __main__ - INFO - Extracting chunk 3: records 100,000 to 150,000
2025-08-05 13:45:45,067 - __main__ - INFO - Processing rebuild chunk 3: 48,167 records
2025-08-05 13:45:46,492 - __main__ - INFO - LanceDB Hybrid Search System Stopped
2025-08-05 13:51:19,630 - __main__ - INFO - ============================================================
2025-08-05 13:51:19,630 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 13:51:19,630 - __main__ - INFO - ============================================================
2025-08-05 13:51:19,631 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 13:51:19,631 - __main__ - INFO - Checking for existing local indexing...
2025-08-05 13:51:20,355 - __main__ - INFO - Found existing shadow table with 142,500 records
2025-08-05 13:51:20,441 - __main__ - INFO - Using existing local indexing
2025-08-05 13:51:20,441 - __main__ - INFO - Promoting existing shadow table to primary
2025-08-05 13:51:20,441 - __main__ - INFO - Starting atomic table switch
2025-08-05 13:51:21,179 - __main__ - INFO - Shadow table validation passed: 142,500 records
2025-08-05 13:51:23,637 - __main__ - ERROR - Failed to update table metadata: lance error: LanceError(Arrow): Arrow error: C Data interface error: Invalid: Casting from timestamp[ns] to timestamp[ms] would lose data: 1754401883618232000. Detail: Python exception: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\code\niche_text_ETL2\.venv\Lib\site-packages\lancedb\table.py", line 301, in gen
    yield pa.Table.from_batches([batch]).cast(reordered_schema).to_batches()[0]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pyarrow\\table.pxi", line 4727, in pyarrow.lib.Table.cast
  File "pyarrow\\table.pxi", line 597, in pyarrow.lib.ChunkedArray.cast
  File "C:\Users\<USER>\Desktop\code\niche_text_ETL2\.venv\Lib\site-packages\pyarrow\compute.py", line 412, in cast
    return call_function("cast", [arr], options, memory_pool)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pyarrow\\_compute.pyx", line 604, in pyarrow._compute.call_function
  File "pyarrow\\_compute.pyx", line 399, in pyarrow._compute.Function.call
  File "pyarrow\\error.pxi", line 155, in pyarrow.lib.pyarrow_internal_check_status
  File "pyarrow\\error.pxi", line 92, in pyarrow.lib.check_status
pyarrow.lib.ArrowInvalid: Casting from timestamp[ns] to timestamp[ms] would lose data: 1754401883618232000
, C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lance-datafusion-0.32.0\src\utils.rs:49:31
2025-08-05 13:51:23,639 - __main__ - INFO - Atomic table switch completed successfully
2025-08-05 13:51:23,639 - __main__ - INFO - New primary table has 142,500 records
2025-08-05 13:51:23,713 - __main__ - INFO - Successfully promoted shadow table to primary
2025-08-05 13:51:24,665 - __main__ - INFO - System resumed successfully with 142,500 existing records
2025-08-05 13:51:24,671 - __main__ - ERROR - Failed to update table metadata: lance error: LanceError(Arrow): Arrow error: C Data interface error: Invalid: Casting from timestamp[ns] to timestamp[ms] would lose data: 1754401884667959000. Detail: Python exception: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\code\niche_text_ETL2\.venv\Lib\site-packages\lancedb\table.py", line 301, in gen
    yield pa.Table.from_batches([batch]).cast(reordered_schema).to_batches()[0]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pyarrow\\table.pxi", line 4727, in pyarrow.lib.Table.cast
  File "pyarrow\\table.pxi", line 597, in pyarrow.lib.ChunkedArray.cast
  File "C:\Users\<USER>\Desktop\code\niche_text_ETL2\.venv\Lib\site-packages\pyarrow\compute.py", line 412, in cast
    return call_function("cast", [arr], options, memory_pool)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pyarrow\\_compute.pyx", line 604, in pyarrow._compute.call_function
  File "pyarrow\\_compute.pyx", line 399, in pyarrow._compute.Function.call
  File "pyarrow\\error.pxi", line 155, in pyarrow.lib.pyarrow_internal_check_status
  File "pyarrow\\error.pxi", line 92, in pyarrow.lib.check_status
pyarrow.lib.ArrowInvalid: Casting from timestamp[ns] to timestamp[ms] would lose data: 1754401884667959000
, C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lance-datafusion-0.32.0\src\utils.rs:49:31
2025-08-05 13:51:24,672 - __main__ - INFO - Created metadata for existing table
2025-08-05 13:51:24,673 - __main__ - INFO - Starting LanceDB Hybrid Search Dual Strategy
2025-08-05 13:51:24,673 - __main__ - INFO - Configuration:
2025-08-05 13:51:24,673 - __main__ - INFO -   - Incremental frequency: 15 minutes
2025-08-05 13:51:24,673 - __main__ - INFO -   - Rebuild frequency: 1 days
2025-08-05 13:51:24,674 - __main__ - INFO -   - Rebuild schedule: 2:00
2025-08-05 13:51:24,674 - __main__ - INFO -   - Chunk size: 50,000 records
2025-08-05 13:51:24,674 - __main__ - INFO -   - Memory limit: 8GB
2025-08-05 13:51:24,674 - __main__ - INFO - Scheduled incremental updates every 15 minutes
2025-08-05 13:51:24,674 - __main__ - INFO - Scheduled daily rebuilds at 2:00
2025-08-05 13:51:26,243 - __main__ - INFO - System status check passed
2025-08-05 13:51:26,243 - __main__ - INFO - Active table has 142,500 records
2025-08-05 13:51:26,244 - __main__ - INFO - Starting scheduler loop...
2025-08-05 13:52:08,740 - __main__ - INFO - Scheduler stopped by user
2025-08-05 13:52:08,740 - __main__ - INFO - LanceDB Hybrid Search System Stopped
