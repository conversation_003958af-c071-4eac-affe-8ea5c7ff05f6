2025-08-05 14:16:47,837 - __main__ - INFO - ============================================================
2025-08-05 14:16:47,837 - __main__ - INFO - LanceDB Hybrid Search System Starting
2025-08-05 14:16:47,838 - __main__ - INFO - ============================================================
2025-08-05 14:16:47,838 - __main__ - INFO - Initializing LanceDB Hybrid Search System
2025-08-05 14:16:47,838 - __main__ - INFO - Checking for existing local indexing...
2025-08-05 14:16:47,842 - __main__ - INFO - INFO: System needs full initialization - no usable existing data found
2025-08-05 14:16:47,843 - __main__ - INFO - No usable existing data found, performing initial data load from SQL Server
2025-08-05 14:16:47,843 - __main__ - INFO - Starting chunked full rebuild for shadow table
2025-08-05 14:16:48,031 - __main__ - INFO - Starting chunked extraction of 1,343,896 records with chunk size 50,000
2025-08-05 14:16:48,031 - __main__ - INFO - Extracting chunk 1: records 0 to 50,000
2025-08-05 14:16:56,785 - __main__ - INFO - Processing rebuild chunk 1: 46,449 records
2025-08-05 14:16:57,762 - __main__ - INFO - Created shadow table report_nlp_shadow with 46,449 records
2025-08-05 14:16:57,764 - __main__ - INFO - Extracting chunk 2: records 50,000 to 100,000
2025-08-05 14:17:07,187 - __main__ - INFO - Processing rebuild chunk 2: 47,884 records
2025-08-05 14:17:08,571 - __main__ - INFO - Added chunk 2 to shadow table: 47,884 records
2025-08-05 14:17:08,573 - __main__ - INFO - Extracting chunk 3: records 100,000 to 150,000
2025-08-05 14:17:18,126 - __main__ - INFO - Processing rebuild chunk 3: 48,167 records
2025-08-05 14:17:19,359 - __main__ - INFO - Added chunk 3 to shadow table: 48,167 records
2025-08-05 14:17:19,531 - __main__ - INFO - Memory status after chunk 3: 1530.4MB used
2025-08-05 14:17:19,533 - __main__ - INFO - Extracting chunk 4: records 150,000 to 200,000
2025-08-05 14:17:29,148 - __main__ - INFO - Processing rebuild chunk 4: 49,770 records
2025-08-05 14:17:30,364 - __main__ - INFO - Added chunk 4 to shadow table: 49,770 records
2025-08-05 14:17:30,366 - __main__ - INFO - Extracting chunk 5: records 200,000 to 250,000
2025-08-05 14:17:40,668 - __main__ - INFO - Processing rebuild chunk 5: 49,970 records
2025-08-05 14:17:41,886 - __main__ - INFO - Added chunk 5 to shadow table: 49,970 records
2025-08-05 14:17:42,065 - __main__ - INFO - Extracting chunk 6: records 250,000 to 300,000
2025-08-05 14:17:54,011 - __main__ - INFO - Processing rebuild chunk 6: 49,996 records
2025-08-05 14:17:55,301 - __main__ - INFO - Added chunk 6 to shadow table: 49,996 records
2025-08-05 14:17:55,488 - __main__ - INFO - Memory status after chunk 6: 1833.6MB used
2025-08-05 14:17:55,490 - __main__ - INFO - Extracting chunk 7: records 300,000 to 350,000
2025-08-05 14:18:05,751 - __main__ - INFO - Processing rebuild chunk 7: 49,995 records
2025-08-05 14:18:07,179 - __main__ - INFO - Added chunk 7 to shadow table: 49,995 records
2025-08-05 14:18:07,182 - __main__ - INFO - Extracting chunk 8: records 350,000 to 400,000
2025-08-05 14:18:17,890 - __main__ - INFO - Processing rebuild chunk 8: 49,968 records
2025-08-05 14:18:19,220 - __main__ - INFO - Added chunk 8 to shadow table: 49,968 records
2025-08-05 14:18:19,222 - __main__ - INFO - Extracting chunk 9: records 400,000 to 450,000
2025-08-05 14:18:33,115 - __main__ - INFO - Processing rebuild chunk 9: 49,990 records
2025-08-05 14:18:34,426 - __main__ - INFO - Added chunk 9 to shadow table: 49,990 records
2025-08-05 14:18:34,607 - __main__ - INFO - Memory status after chunk 9: 2052.5MB used
2025-08-05 14:18:34,609 - __main__ - INFO - Extracting chunk 10: records 450,000 to 500,000
2025-08-05 14:18:45,511 - __main__ - INFO - Processing rebuild chunk 10: 49,989 records
2025-08-05 14:18:46,845 - __main__ - INFO - Added chunk 10 to shadow table: 49,989 records
2025-08-05 14:18:47,052 - __main__ - INFO - Extracting chunk 11: records 500,000 to 550,000
2025-08-05 14:18:58,542 - __main__ - INFO - Processing rebuild chunk 11: 49,996 records
2025-08-05 14:18:59,923 - __main__ - INFO - Added chunk 11 to shadow table: 49,996 records
2025-08-05 14:18:59,925 - __main__ - INFO - Extracting chunk 12: records 550,000 to 600,000
2025-08-05 14:19:12,416 - __main__ - INFO - Processing rebuild chunk 12: 49,996 records
2025-08-05 14:19:13,889 - __main__ - INFO - Added chunk 12 to shadow table: 49,996 records
2025-08-05 14:19:14,076 - __main__ - INFO - Memory status after chunk 12: 2290.7MB used
2025-08-05 14:19:14,079 - __main__ - INFO - Extracting chunk 13: records 600,000 to 650,000
2025-08-05 14:19:26,933 - __main__ - INFO - Processing rebuild chunk 13: 49,992 records
2025-08-05 14:19:28,315 - __main__ - INFO - Added chunk 13 to shadow table: 49,992 records
2025-08-05 14:19:28,317 - __main__ - INFO - Extracting chunk 14: records 650,000 to 700,000
2025-08-05 14:19:39,244 - __main__ - INFO - Processing rebuild chunk 14: 49,997 records
2025-08-05 14:19:40,540 - __main__ - INFO - Added chunk 14 to shadow table: 49,997 records
2025-08-05 14:19:40,542 - __main__ - INFO - Extracting chunk 15: records 700,000 to 750,000
2025-08-05 14:19:51,120 - __main__ - INFO - Processing rebuild chunk 15: 49,993 records
2025-08-05 14:19:52,433 - __main__ - INFO - Added chunk 15 to shadow table: 49,993 records
2025-08-05 14:19:52,621 - __main__ - INFO - Memory status after chunk 15: 2171.1MB used
2025-08-05 14:19:52,804 - __main__ - INFO - Extracting chunk 16: records 750,000 to 800,000
2025-08-05 14:20:03,969 - __main__ - INFO - Processing rebuild chunk 16: 49,987 records
2025-08-05 14:20:05,185 - __main__ - INFO - Added chunk 16 to shadow table: 49,987 records
2025-08-05 14:20:05,187 - __main__ - INFO - Extracting chunk 17: records 800,000 to 850,000
2025-08-05 14:20:16,126 - __main__ - INFO - Processing rebuild chunk 17: 49,988 records
2025-08-05 14:20:17,428 - __main__ - INFO - Added chunk 17 to shadow table: 49,988 records
2025-08-05 14:20:17,430 - __main__ - INFO - Extracting chunk 18: records 850,000 to 900,000
2025-08-05 14:20:28,400 - __main__ - INFO - Processing rebuild chunk 18: 49,994 records
2025-08-05 14:20:29,645 - __main__ - INFO - Added chunk 18 to shadow table: 49,994 records
2025-08-05 14:20:29,824 - __main__ - INFO - Memory status after chunk 18: 2529.6MB used
2025-08-05 14:20:29,826 - __main__ - INFO - Extracting chunk 19: records 900,000 to 950,000
2025-08-05 14:20:43,926 - __main__ - INFO - Processing rebuild chunk 19: 49,991 records
2025-08-05 14:20:45,139 - __main__ - INFO - Added chunk 19 to shadow table: 49,991 records
2025-08-05 14:20:45,141 - __main__ - INFO - Extracting chunk 20: records 950,000 to 1,000,000
2025-08-05 14:20:58,721 - __main__ - INFO - Processing rebuild chunk 20: 49,993 records
2025-08-05 14:21:00,800 - __main__ - INFO - Added chunk 20 to shadow table: 49,993 records
2025-08-05 14:21:01,060 - __main__ - INFO - Extracting chunk 21: records 1,000,000 to 1,050,000
2025-08-05 14:21:14,016 - __main__ - INFO - Processing rebuild chunk 21: 49,980 records
2025-08-05 14:21:15,351 - __main__ - INFO - Added chunk 21 to shadow table: 49,980 records
2025-08-05 14:21:15,537 - __main__ - INFO - Memory status after chunk 21: 2592.3MB used
2025-08-05 14:21:15,539 - __main__ - INFO - Extracting chunk 22: records 1,050,000 to 1,100,000
2025-08-05 14:21:28,599 - __main__ - INFO - Processing rebuild chunk 22: 49,990 records
2025-08-05 14:21:29,986 - __main__ - INFO - Added chunk 22 to shadow table: 49,990 records
2025-08-05 14:21:29,989 - __main__ - INFO - Extracting chunk 23: records 1,100,000 to 1,150,000
2025-08-05 14:21:42,694 - __main__ - INFO - Processing rebuild chunk 23: 49,992 records
2025-08-05 14:21:43,983 - __main__ - INFO - Added chunk 23 to shadow table: 49,992 records
2025-08-05 14:21:43,987 - __main__ - INFO - Extracting chunk 24: records 1,150,000 to 1,200,000
2025-08-05 14:21:56,133 - __main__ - INFO - Processing rebuild chunk 24: 49,988 records
2025-08-05 14:21:57,481 - __main__ - INFO - Added chunk 24 to shadow table: 49,988 records
2025-08-05 14:21:57,686 - __main__ - INFO - Memory status after chunk 24: 2613.5MB used
2025-08-05 14:21:57,689 - __main__ - INFO - Extracting chunk 25: records 1,200,000 to 1,250,000
2025-08-05 14:22:09,420 - __main__ - INFO - Processing rebuild chunk 25: 49,985 records
2025-08-05 14:22:10,873 - __main__ - INFO - Added chunk 25 to shadow table: 49,985 records
2025-08-05 14:22:11,065 - __main__ - INFO - Extracting chunk 26: records 1,250,000 to 1,300,000
2025-08-05 14:22:23,107 - __main__ - INFO - Processing rebuild chunk 26: 49,949 records
2025-08-05 14:22:24,368 - __main__ - INFO - Added chunk 26 to shadow table: 49,949 records
2025-08-05 14:22:24,370 - __main__ - INFO - Extracting chunk 27: records 1,300,000 to 1,343,896
2025-08-05 14:22:35,991 - __main__ - INFO - Processing rebuild chunk 27: 43,787 records
2025-08-05 14:22:37,294 - __main__ - INFO - Added chunk 27 to shadow table: 43,787 records
2025-08-05 14:22:37,470 - __main__ - INFO - Memory status after chunk 27: 2530.2MB used
2025-08-05 14:22:37,472 - __main__ - INFO - Completed chunked extraction of 1,343,896 records in 27 chunks
2025-08-05 14:22:37,488 - __main__ - INFO - Created metadata table and added record for report_nlp_shadow
2025-08-05 14:22:37,488 - __main__ - INFO - Shadow table rebuild completed successfully:
2025-08-05 14:22:37,489 - __main__ - INFO -   - Total records: 1,335,776
2025-08-05 14:22:37,489 - __main__ - INFO -   - Chunks processed: 27
2025-08-05 14:22:37,489 - __main__ - INFO -   - Time elapsed: 349.6 seconds
2025-08-05 14:22:37,490 - __main__ - INFO -   - Processing rate: 3820.4 records/second
2025-08-05 14:22:37,768 - __main__ - INFO - Starting atomic table switch
2025-08-05 14:22:48,618 - __main__ - INFO - Shadow table validation passed: 1,335,776 records
2025-08-05 14:23:25,151 - __main__ - ERROR - Atomic switch failed, attempting rollback: lance error: LanceError(IO): Generic LocalFileSystem error: Unable to copy data to file: There is not enough space on the disk. (os error 112), C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lance-file-0.32.0\src\v2\writer.rs:186:9
2025-08-05 14:23:25,160 - __main__ - ERROR - No backup available for rollback
2025-08-05 14:23:28,654 - __main__ - ERROR - System initialization failed during table switch
2025-08-05 14:23:28,654 - __main__ - ERROR - System initialization failed, exiting
2025-08-05 14:23:28,655 - __main__ - INFO - LanceDB Hybrid Search System Stopped
