# FastAPI Layer for LanceDB Hybrid Search System
# API interface for the hybrid search implementation

from fastapi import FastAPI, HTTPException, Query, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Dict, Any
import asyncio
import uvicorn
from datetime import datetime
import logging

# Import from the main implementation
from lancedb_hybrid_search import (
    hybrid_search,
    vector_search,
    fulltext_search,
    get_system_status,
    run_incremental_updates,
    schedule_shadow_rebuild,
    initialize_system,
    DB_PATH,
    logger
)

# =============================================================================
# FASTAPI APP SETUP
# =============================================================================

app = FastAPI(
    title="LanceDB Hybrid Search API",
    description="High-performance hybrid search API using LanceDB with shadow table architecture",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =============================================================================
# REQUEST/RESPONSE MODELS
# =============================================================================

class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query text")
    semantic_weight: float = Field(0.6, ge=0.0, le=1.0, description="Weight for semantic search")
    lexical_weight: float = Field(0.4, ge=0.0, le=1.0, description="Weight for lexical search")
    top_k: int = Field(10, ge=1, le=100, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")
    include_scores: bool = Field(True, description="Include relevance scores")
    search_mode: str = Field("hybrid", description="Search mode: hybrid, vector, fulltext")

class SearchResponse(BaseModel):
    success: bool
    query: str
    total_results: int
    execution_time_ms: float
    results: List[Dict[str, Any]]
    search_parameters: Dict[str, Any]
    table_version: Optional[int] = None

class SystemStatusResponse(BaseModel):
    success: bool
    timestamp: str
    active_table: Dict[str, Any]
    performance: Dict[str, float]
    configuration: Dict[str, Any]
    tables: List[str]

class OperationResponse(BaseModel):
    success: bool
    message: str
    timestamp: str
    details: Optional[Dict[str, Any]] = None

# =============================================================================
# SEARCH ENDPOINTS
# =============================================================================

@app.post("/search", response_model=SearchResponse)
async def hybrid_search_endpoint(request: SearchRequest):
    """Full hybrid search with native LanceDB features"""
    try:
        logger.info(f"Hybrid search request: '{request.query}' (mode: {request.search_mode})")
        
        # Route to appropriate search function based on mode
        if request.search_mode == "vector" and hasattr(request, 'query_vector'):
            # Vector search requires embedding the query first
            # For now, fallback to hybrid search
            result = hybrid_search(
                query=request.query,
                filters=request.filters,
                semantic_weight=request.semantic_weight,
                lexical_weight=request.lexical_weight,
                top_k=request.top_k
            )
        elif request.search_mode == "fulltext":
            # Full-text search
            search_results = fulltext_search(
                query=request.query,
                filters=request.filters,
                limit=request.top_k
            )
            result = {
                "success": True,
                "query": request.query,
                "total_results": len(search_results),
                "execution_time_ms": 0,  # Would need timing
                "results": search_results,
                "search_parameters": request.dict()
            }
        else:
            # Default hybrid search
            result = hybrid_search(
                query=request.query,
                filters=request.filters,
                semantic_weight=request.semantic_weight,
                lexical_weight=request.lexical_weight,
                top_k=request.top_k
            )
        
        if result["success"]:
            return SearchResponse(
                success=True,
                query=request.query,
                total_results=result["total_results"],
                execution_time_ms=result["execution_time_ms"],
                results=result["results"],
                search_parameters=result["search_parameters"],
                table_version=1  # Placeholder
            )
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "Search failed"))
            
    except Exception as e:
        logger.error(f"Search endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search", response_model=SearchResponse)
async def simple_search(
    q: str = Query(..., description="Search query"),
    mode: str = Query("hybrid", description="Search mode: hybrid, vector, fulltext"),
    semantic_weight: float = Query(0.6, ge=0.0, le=1.0, description="Semantic search weight"),
    lexical_weight: float = Query(0.4, ge=0.0, le=1.0, description="Lexical search weight"),
    top_k: int = Query(10, ge=1, le=100, description="Number of results"),
    # Metadata filters
    zone: Optional[str] = Query(None, description="Filter by zone"),
    team: Optional[str] = Query(None, description="Filter by team"),
    municipality: Optional[str] = Query(None, description="Filter by municipality"),
    category: Optional[str] = Query(None, description="Filter by category"),
    date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)")
):
    """Simple search with URL parameters"""
    try:
        # Build filters from query parameters
        filters = {}
        if zone:
            filters["zone"] = zone
        if team:
            filters["team"] = team
        if municipality:
            filters["municipality"] = municipality
        if category:
            filters["category"] = category
        if date_from or date_to:
            # Handle date range filters
            if date_from and date_to:
                filters["date_range"] = {"from": date_from, "to": date_to}
            elif date_from:
                filters["date_from"] = date_from
            elif date_to:
                filters["date_to"] = date_to
        
        # Create search request
        search_request = SearchRequest(
            query=q,
            semantic_weight=semantic_weight,
            lexical_weight=lexical_weight,
            top_k=top_k,
            filters=filters if filters else None,
            search_mode=mode
        )
        
        # Execute search
        return await hybrid_search_endpoint(search_request)
        
    except Exception as e:
        logger.error(f"Simple search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/advanced")
async def advanced_search(request: Dict[str, Any]):
    """Advanced search with complex query builder"""
    try:
        # Parse advanced search request
        query = request.get("query", "")
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")
        
        # Extract advanced parameters
        search_mode = request.get("mode", "hybrid")
        filters = request.get("filters", {})
        options = request.get("options", {})
        
        # Build search request
        search_request = SearchRequest(
            query=query,
            semantic_weight=options.get("semantic_weight", 0.6),
            lexical_weight=options.get("lexical_weight", 0.4),
            top_k=options.get("top_k", 10),
            filters=filters if filters else None,
            search_mode=search_mode,
            include_scores=options.get("include_scores", True)
        )
        
        return await hybrid_search_endpoint(search_request)
        
    except Exception as e:
        logger.error(f"Advanced search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# MANAGEMENT ENDPOINTS
# =============================================================================

@app.get("/status", response_model=SystemStatusResponse)
async def get_system_status_endpoint():
    """System health and table status"""
    try:
        status = get_system_status()
        
        if status["success"]:
            return SystemStatusResponse(
                success=True,
                timestamp=status["timestamp"],
                active_table=status.get("active_table", {}),
                performance=status.get("performance", {}),
                configuration=status.get("configuration", {}),
                tables=status.get("tables", [])
            )
        else:
            raise HTTPException(status_code=500, detail=status.get("error", "Status check failed"))
            
    except Exception as e:
        logger.error(f"Status endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/update/trigger", response_model=OperationResponse)
async def trigger_incremental_update(background_tasks: BackgroundTasks):
    """Trigger manual incremental update"""
    try:
        # Add incremental update to background tasks
        background_tasks.add_task(run_incremental_updates)
        
        return OperationResponse(
            success=True,
            message="Incremental update triggered successfully",
            timestamp=datetime.now().isoformat(),
            details={"background_task": True}
        )
        
    except Exception as e:
        logger.error(f"Trigger update error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rebuild/trigger", response_model=OperationResponse)
async def trigger_shadow_rebuild(background_tasks: BackgroundTasks):
    """Trigger manual shadow table rebuild"""
    try:
        # Add shadow rebuild to background tasks
        background_tasks.add_task(schedule_shadow_rebuild)
        
        return OperationResponse(
            success=True,
            message="Shadow table rebuild triggered successfully",
            timestamp=datetime.now().isoformat(),
            details={"background_task": True}
        )
        
    except Exception as e:
        logger.error(f"Trigger rebuild error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/tables/info")
async def get_table_info():
    """Primary/shadow table information"""
    try:
        status = get_system_status()
        
        if status["success"]:
            return {
                "success": True,
                "active_table": status.get("active_table", {}),
                "metadata": status.get("metadata", {}),
                "tables": status.get("tables", []),
                "timestamp": status["timestamp"]
            }
        else:
            raise HTTPException(status_code=500, detail=status.get("error", "Failed to get table info"))
            
    except Exception as e:
        logger.error(f"Table info error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/stats")
async def get_performance_stats():
    """Search performance metrics"""
    try:
        status = get_system_status()
        
        if status["success"]:
            performance = status.get("performance", {})
            return {
                "success": True,
                "performance_metrics": performance,
                "timestamp": status["timestamp"]
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to get performance stats")
            
    except Exception as e:
        logger.error(f"Performance stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "LanceDB Hybrid Search API"
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "service": "LanceDB Hybrid Search API",
        "version": "1.0.0",
        "description": "High-performance hybrid search API using LanceDB with shadow table architecture",
        "endpoints": {
            "search": "/search (GET/POST)",
            "status": "/status",
            "health": "/health",
            "docs": "/docs"
        },
        "timestamp": datetime.now().isoformat()
    }

# =============================================================================
# ERROR HANDLERS
# =============================================================================

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

# =============================================================================
# STARTUP/SHUTDOWN EVENTS
# =============================================================================

@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    logger.info("Starting LanceDB Hybrid Search API...")
    
    # Initialize the hybrid search system
    try:
        success = initialize_system(DB_PATH)
        if success:
            logger.info("Hybrid search system initialized successfully")
        else:
            logger.error("Failed to initialize hybrid search system")
    except Exception as e:
        logger.error(f"Startup initialization error: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down LanceDB Hybrid Search API...")

# =============================================================================
# MAIN FUNCTION
# =============================================================================

def main():
    """Run the FastAPI server"""
    uvicorn.run(
        "lancedb_hybrid_search_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Set to True for development
        log_level="info"
    )

if __name__ == "__main__":
    main()
